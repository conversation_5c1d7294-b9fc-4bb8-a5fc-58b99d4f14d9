#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de teste para a funcionalidade de consulta de sinal por serial
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db import get_olt_type_by_serial, findauthorizedonu

def test_get_olt_type_by_serial():
    """Testa a função get_olt_type_by_serial"""
    print("=== Testando get_olt_type_by_serial ===")
    
    # Teste com serial fictício
    test_serial = "FHTT12345678"
    
    try:
        result = get_olt_type_by_serial(test_serial)
        print(f"Serial: {test_serial}")
        print(f"Resultado: {result}")
        
        if result:
            print(f"Tipo de OLT encontrado: {result}")
        else:
            print("Serial não encontrado ou não vinculado a um OLT")
            
    except Exception as e:
        print(f"Erro ao testar get_olt_type_by_serial: {e}")

def test_findauthorizedonu():
    """Testa a função findauthorizedonu"""
    print("\n=== Testando findauthorizedonu ===")
    
    # Teste com serial fictício
    test_serial = "FHTT12345678"
    
    try:
        result = findauthorizedonu(serial=test_serial)
        print(f"Serial: {test_serial}")
        print(f"Resultado: {result}")
        
        if result:
            print("ONU encontrada:")
            for key, value in result.items():
                print(f"  {key}: {value}")
        else:
            print("ONU não encontrada")
            
    except Exception as e:
        print(f"Erro ao testar findauthorizedonu: {e}")

if __name__ == "__main__":
    test_get_olt_type_by_serial()
    test_findauthorizedonu()
