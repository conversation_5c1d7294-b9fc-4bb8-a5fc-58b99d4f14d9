#!/usr/bin/python
# -*- coding: utf-8 -*-

from psycopg2 import connect, sql
import psycopg2.extras
import pymysql.cursors
from dotenv import load_dotenv
import os

def __unmdb():
  try:  
    load_dotenv()
    con = pymysql.connect(
        host=os.getenv('UNMDB_HOST'),
        user=os.getenv('UNMDB_USER'),
        password=os.getenv('UNMDB_PASS'),
        db='integratecfgdb',
        charset='utf8mb4',
        cursorclass=pymysql.cursors.DictCursor)
    return con
  except Exception as e:
    print('Falha em unmdb: '+ str(e)) 

def __nocdb():
    try:
        load_dotenv()
        con = psycopg2.connect(
            host=os.getenv('SERVICES1DB_HOST'),
            database=os.getenv('SERVICES1DB_DATABASE'),
            user=os.getenv('SERVICES1DB_USER'),
            password=os.getenv('SERVICES1DB_PASS')
        )
        return con
    except Exception as e:
        #app.logger.error(str(e))    
        print(str(e))

def __ixcdb():
    try:
        load_dotenv()
        con = pymysql.connect(host=os.getenv('IXC_HOST'),
                              user=os.getenv('IXC_USER'),
                              password=os.getenv('IXC_PASS'),
                              db=os.getenv('IXC_DATABASE'),
                              charset='utf8mb4',
                              cursorclass=pymysql.cursors.DictCursor)
        return con
    except Exception as e:
        print('Falha em ixcdb: ' + str(e))

def olt_model(ip):
    try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        sql = 'select * from authservice.olts where ip = %s'
        cur.execute(sql, (ip,))
        recset = cur.fetchone()
        if(recset):
            return recset['fabricante_modelo']
        else:
            return False    
        con.close()
        return recset
    except Exception as e:
        print(str(e))

def olt_list(model='ZTE'):
    try:

        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        sql = 'select * from authservice.olts where fabricante_modelo = %s'
        cur.execute(sql, (model,))
        recset = cur.fetchall()
        con.close()
        return recset
    except Exception as e:
        print(str(e))
        #app.logger.error(str(e))

def onu_list(**kwargs):
    try:

        valid = ['serial', 'nome', 'olt_ip', 'placa', 'porta']
        if(len(kwargs) > 0):
            con = __nocdb()
            cur = con.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            params = {}
            # adiciona apenas os parametros validos
            for key, value in kwargs.items():
                if(key in valid):
                    params[key] = value

            # filtra pelos parametros informados
            where_clause = 'WHERE ' + \
                ' AND '.join([k + ' = %s' for k in params.keys()])
            params = tuple(params.values())

            sql = "SELECT serial, onuid, olt_ip as olt, placa, porta, nome, modelo from fdwtables.onus " + where_clause

            cur.execute(sql, (params))
            recset = cur.fetchall()
            con.close()
            return recset
    except Exception as e:
        print("Ocorreu um erro em onu_list: {0}".format(e))
        raise

def getonu(**kwargs):
    try:

        valid = ['serial', 'nome', 'olt_ip', 'placa', 'porta']
        if(len(kwargs) > 0):
            con = __nocdb()
            cur = con.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            params = {}
            # adiciona apenas os parametros validos
            for key, value in kwargs.items():
                if(key in valid):
                    params[key] = value

            # filtra pelos parametros informados
            where_clause = 'WHERE ' + \
                ' AND '.join([k + ' = %s' for k in params.keys()])
            params = tuple(params.values())

            sql = "SELECT serial, onuid, olt_ip as olt, placa, porta, nome, modelo, olt_modelo from ftth.onus " + where_clause

            cur.execute(sql, (params))
            recset = cur.fetchall()
            con.close()
            return recset
    except Exception as e:
        print("Ocorreu um erro em getonu: {0}".format(e))
        raise

def getdevice(serial=None, olt=None):
  try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        if(serial):
            sql_query = "SELECT * from authservice.devices where upper(serial) = upper(%s)"
            cur.execute(sql_query, (serial,))
            recset = cur.fetchone()

        if(olt):
            sql_query = "SELECT * from authservice.devices where (olt_ip) = %s order by chassi, slot, pon, id"
            cur.execute(sql_query, (olt,))
            recset = cur.fetchall()

        con.close()
        if(recset):
            return recset
        else:
            return {}    

  except Exception as e:
       # app.logger.error(str(e))
       print(str(e))

def removedevice(serial=None, olt=None, chassi=None, slot=None, pon=None, onuid=None):
  try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        if(serial):
            sql_query = "DELETE from authservice.devices where upper(serial) = upper(%s)"
            cur.execute(sql_query, (serial,))
            con.commit()
        elif(all([olt, chassi, slot, pon, onuid])):    
            sql_query = "DELETE from authservice.devices where olt_ip=%s and chassi=%s and slot=%s and pon=%s and onuid=%s"
            cur.execute(sql_query, (olt,chassi,slot,pon,onuid,))
            con.commit()
        con.close()

  except Exception as e:
       # app.logger.error(str(e))
       print(str(e))

def updatedevice(device):
    try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.DictCursor)
        valid_fields = ['serial_acs', 'mac', 'patrimonio', 'name', 'olt', 'olt_ip', 'olt_modelo', 'chassi', 'slot', 'pon', 'onuid', 'type', 'onu_modelo', 'vlan_tr069', 'vlan_pppoe', 'vlan_iptv_unicast', 'vlan_iptv_multicast', 'serial', 'script']

        #filtra os campos extras, permitindo apenas os campos iguais ao valid_fields
        device = {k: v for k, v in device.items() if k in valid_fields}

        if('serial' in device):
          sql_query = "SELECT * from authservice.devices where upper(serial) = upper(%s)"
          cur.execute(sql_query, (device['serial'],))
          update_query = sql.SQL("UPDATE authservice.devices SET {data} WHERE serial = {serial}").format(
            data=sql.SQL(', ').join(
              sql.Composed([sql.Identifier(k), sql.SQL(" = "), sql.Placeholder(k)]) for k in device.keys()
            ),
            serial=sql.Placeholder('serial')
          )
        elif('mac' in device):
          sql_query = "SELECT * from authservice.devices where replace(upper(mac), ':', '') = replace(upper(%s), ':', '')"
          cur.execute(sql_query, (device['mac'],))
          update_query = sql.SQL("UPDATE authservice.devices SET {data} WHERE replace(upper(mac), ':', '') = replace(upper({mac}), ':', '')").format(
            data=sql.SQL(', ').join(
              sql.Composed([sql.Identifier(k), sql.SQL(" = "), sql.Placeholder(k)]) for k in device.keys()
            ),
            mac=sql.Placeholder('mac')
          )
        else:
          return False  
        recset = cur.fetchone()
        if(recset):
          #print(cur.mogrify(update_query, device))
          with con:
            with con.cursor() as cur:
              cur.execute(update_query, device)
        else:
          insert_query = sql.SQL("INSERT INTO authservice.devices ({columns}) VALUES ({data})").format(
            columns=sql.SQL(', ').join(
              sql.Composed([sql.Identifier(k)]) for k in device.keys()),
            data=sql.SQL(",").join(map(sql.Placeholder, device))
          )     
          #print(cur.mogrify(insert_query, device))
          with con:
            with con.cursor() as cur:
              cur.execute(insert_query, device)
    except Exception as e:
        print('Ocorreu um erro em updatedevice: '+ str(e))       

def get_dados_tecnicos(serial, ip_olt, interface):
  try:
    con = __ixcdb()
    with con.cursor() as cur:
      sql = '''
             select ru.id, rp.id_projeto as id_df_projeto, rpr.id as id_transmissor, rrp.id as interface_transmissao,
             ru.id_cliente, ru.id_grupo, ru.senha, ru.login, ru.login_simultaneo, ru.ativo, ru.ip, ru.mac, ru.obs, 
             ru.auto_preencher_ip, ru.auto_preencher_mac, ru.fixar_ip, ru.id_contrato, ru.autenticacao_por_mac, 
             ru.autenticacao, ru.cache, ru.relacionar_ip_ao_login, ru.relacionar_mac_ao_login, ru.online, ru.concentrador, 
             ru.conexao, ru.tipo_conexao, ru.porta_http, ru.id_concentrador, ru.interface, ru.latitude, ru.longitude, 
             ru.tipo_conexao_mapa, ru.senha_md5, ru.ip_aviso, ru.id_caixa_ftth, ru.senha_router1, ru.senha_router2, 
             ru.senha_rede_sem_fio, ru.ftth_porta, ru.id_porta_transmissor, ru.cliente_tem_a_senha, ru.autenticacao_wps, 
             ru.autenticacao_mac, ru.autenticacao_wpa, ru.tipo_vinculo_plano, ru.tempo_conectado, ru.id_hardware, 
             ru.tipo_equipamento, ru.metragem_interna, ru.metragem_externa, ru.tronco, ru.splitter, 
             ru.sinal_ultimo_atendimento, ru.porta_router2, ru.franquia_maximo, ru.franquia_atingida, 
             ru.endereco, ru.endereco_padrao_cliente, ru.numero, ru.bairro, ru.cidade, ru.cep, ru.complemento, 
             ru.referencia, ru.id_condominio, ru.ssid_router_wifi, ru.bloco, ru.apartamento, rrp.vlan_pppoe as vlan, ru.vlan_ip_rede, 
             ru.rota, ru.agent_circuit_id, ru.usuario_router1, ru.pd_ipv6, ru.perfil_autorizar_onu, ru.download_atual, 
             ru.upload_atual, ru.relacionar_concentrador_ao_login, ru.pool_radius, ru.id_rad_dns, ru.modelo_tranmissor, 
             ru.gw_vlan, ru.motivo_desconexao, ru.count_desconexao, ru.tempo_conexao, ru.fixar_ipv6, 
             ru.auto_preencher_ipv6, ru.relacionar_ipv6_ao_login, ru.id_filial, ru.ip_aux, ru.porta_aux, 
             ru.ponta, ru.id_radgrupos_pools, ru.service_tag_vlan, ru.framed_fixar_ipv6, ru.framed_autopreencher_ipv6, 
             ru.framed_relacionar_ipv6_ao_login, ru.framed_relaciona_ipv6_ao_login, ru.framed_pd_ipv6, ru.id_integracao, 
             ru.lte_auth_key, ru.lte_auth_opc, ru.lte_id, ru.lte_apns from ixcprovedor.radpop_radio rpr
             left join ixcprovedor.radpop rp on(rp.id=rpr.id_pop)
             left join ixcprovedor.radpop_radio_porta rrp on(rrp.id_pop_radio=rpr.id)
             left join ixcprovedor.patrimonio p on(upper(p.serial_fornecedor)=upper(%s))
             left join ixcprovedor.radusuarios ru on(replace(upper(ru.mac), ':', '')=replace(upper(p.id_mac), ':', ''))
             where rpr.ip = %s and rrp.interface = %s
            '''
      cur.execute(sql, (serial, ip_olt, interface))
      recset = cur.fetchone()
      con.close()

      #recset['endereco'] = recset['endereco'].encode('utf-8')
    return recset

  except Exception as e:
    print('Falha em get_dados_tecnicos: '+ str(e))

def provision_jobs_list():
    try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        sql = 'select * from authservice.view_provisions limit 50'
        cur.execute(sql,)
        recset = cur.fetchall()
        con.close()
        return recset
    except Exception as e:
        print(str(e))
        #app.logger.error(str(e))    

def provision_list(serial=None,status=None):
    try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        if(serial == None):
          if(status == None):
            sql = '''
              SELECT p.* FROM authservice.provisions p
              order by p.enqueued_at desc limit 50
              '''
            cur.execute(sql,)
          else:
            sql = '''
              SELECT p.* FROM authservice.provisions p
              where status = %s
              order by p.enqueued_at desc limit 50
              '''  
            cur.execute(sql,(status,))  
        else:
          sql = '''
              SELECT p.* FROM authservice.provisions p
              where upper(p.serial) = upper(%s)
              order by p.enqueued_at desc limit 50
              '''
          cur.execute(sql,(serial,))

        




        recset = cur.fetchall()
        con.close()
        return recset 
    except Exception as e:
        print(str(e))
        #app.logger.error(str(e))    

def job_list(provision_id):
    try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        sql = 'select * from authservice.jobs where provision_id = %s order by started_at'
        cur.execute(sql,(provision_id,))
        recset = cur.fetchall()
        con.close()
        return recset
    except Exception as e:
        print(str(e))
        #app.logger.error(str(e))           

# Localiza informacoes das vlans
def getvlans(ip_olt, placa, porta):
  try:
    con = __ixcdb()
    with con.cursor() as cur:
        sql = '''
               SELECT olt.descricao as nome_olt, placa.descricao as nome_placa, porta.interface as interface_porta, 
               porta.vlan_pppoe, porta.vlan_iptv FROM ixcprovedor.radpop_radio olt
               inner join ixcprovedor.radpop_olt_slot placa on (placa.id_transmissor = olt.id)
               inner join ixcprovedor.radpop_radio_porta porta on (porta.id_slot = placa.id)
               where olt.ip = %s and placa.numero_slot = %s and porta.numero_pon = %s
        ''' 
        cur.execute(sql,(ip_olt,placa,porta,))
        recset = cur.fetchone()
        con.close()
    return recset
  except Exception as e:
    errorlog('Falha em getvlans: '+ str(e))
  
def get_olt_info_by_serial(serial):
    """
    Verifica se o serial está vinculado a um usuário e retorna o tipo e IP da OLT

    Parameters
    ----------
    serial : str
        Serial da ONU

    Returns
    -------
    dict or None
        {'fabricante_modelo': 'FBT'/'ZTE', 'ip': 'IP_DA_OLT'} se encontrado, None caso contrário
    """
    try:
        con = __nocdb()
        cur = con.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        sql = '''
            SELECT o.fabricante_modelo, o.ip
            FROM fdwtables.ixc_patrimonio p
            LEFT JOIN fdwtables.ixc_radusuarios ru
                ON UPPER(REGEXP_REPLACE(ru.mac, '[^A-Fa-f0-9]', '', 'g')) = UPPER(REGEXP_REPLACE(p.id_mac, '[^A-Fa-f0-9]', '', 'g'))
            LEFT JOIN fdwtables.ixc_radpop_radio rpr on rpr.id = ru.id_transmissor
            LEFT JOIN authservice.olts o ON o.ip = rpr.ip
            WHERE p.serial_fornecedor = %s
        '''
        cur.execute(sql, (serial,))
        recset = cur.fetchone()
        con.close()

        if recset and recset['fabricante_modelo'] and recset['ip']:
            return {
                'fabricante_modelo': recset['fabricante_modelo'],
                'ip': recset['ip']
            }
        else:
            return None

    except Exception as e:
        print('Falha em get_olt_info_by_serial: '+ str(e))
        return None

def findauthorizedonu(serial=None, nome=None,olt=None):
  try:
    con = __unmdb()
    with con.cursor() as cur:
        sql = '''
                SELECT CONVERT_TZ(d.ccreatetime ,'+00:00','-03:00') as data_provisionamento,
                upper(d.contmacquery) as serial, d.cauthno as onuid, d.cobjectname as nome,
                d.cslotno as placa, d.cponno as porta, n.cobjectname as olt_nome,
                n.cipaddress as olt_ip, d.cequipmentid as modelo FROM integratecfgdb.t_ontdevice d
                inner join integratecfgdb.t_nedevice n on (n.cobjectid = d.cneid)
                where {}
              '''
        if(serial):
          sql = sql.format('upper(d.contmac) = upper(%s) order by n.cobjectname desc')
          cur.execute(sql,(serial,))
          recset = cur.fetchone()
        elif(nome):
          sql = sql.format('upper(d.cobjectname) = upper(%s) order by n.cobjectname desc')
          cur.execute(sql,(nome,))
          recset = cur.fetchall()
        con.close()
        return recset
  except Exception as e:
    print('Falha em finauthorizeddonu: '+ str(e))