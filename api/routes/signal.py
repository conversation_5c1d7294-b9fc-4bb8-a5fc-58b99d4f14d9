from flask import jsonify, Blueprint
from db import *
from models import token_required
from fhmanager import FHManager
from ztemanager import ZTEManager
import re

signal_route = Blueprint('signal_route', __name__, template_folder='templates')

def normalize_signal_data(signal_data, olt_type):
    """
    Padroniza os dados de sinal para um formato comum

    Parameters
    ----------
    signal_data : dict
        Dados brutos do sinal da OLT
    olt_type : str
        Tipo da OLT ('FBT' ou 'ZTE')

    Returnsy
    -------
    dict
        Dados padronizados do sinal
    """
    try:
        normalized = {
            'onu_rx_power': None,  # Sinal de recepção na ONU
            'onu_tx_power': None,  # Sinal de transmissão da ONU
            'olt_rx_power': None   # Sinal de recepção do OLT
        }

        if olt_type == 'FBT':
            # Para FiberHome
            normalized['onu_rx_power'] = signal_data.get('recvpower')
            normalized['onu_tx_power'] = signal_data.get('sendpower')
            normalized['olt_rx_power'] = signal_data.get('oltrecvpower')

        elif olt_type == 'ZTE':
            # Para ZTE, extrair valores numéricos removendo "(dbm)"
            if signal_data.get('down') and signal_data['down'].get('onu'):
                onu_rx = signal_data['down']['onu']
                # Remove "(dbm)" e extrai o número
                normalized['onu_rx_power'] = re.sub(r'\(.*?\)', '', onu_rx).strip()

            if signal_data.get('up') and signal_data['up'].get('onu'):
                onu_tx = signal_data['up']['onu']
                normalized['onu_tx_power'] = re.sub(r'\(.*?\)', '', onu_tx).strip()

            if signal_data.get('up') and signal_data['up'].get('olt'):
                olt_rx = signal_data['up']['olt']
                normalized['olt_rx_power'] = re.sub(r'\(.*?\)', '', olt_rx).strip()

        return normalized

    except Exception as e:
        print(f"Erro ao normalizar dados de sinal: {e}")
        return {
            'onu_rx_power': None,
            'onu_tx_power': None,
            'olt_rx_power': None
        }

@signal_route.route('/signal/<serial>', methods=['GET'])
@token_required
def get_onu_signal(serial):
    try:
        # Serial vem como parâmetro da URL
        # Verificar tipo e IP da OLT através do serial
        olt_info = get_olt_info_by_serial(serial)

        if not olt_info:
            return jsonify({'status': 'ERROR', 'msg': 'Serial não encontrado ou não vinculado a um OLT'})

        olt_type = olt_info['fabricante_modelo']
        olt_ip = olt_info['ip']

        if olt_type == 'FBT':
            # Para FiberHome, precisamos dos parâmetros da ONU autorizada
            onu_info = findauthorizedonu(serial=serial)

            if not onu_info:
                return jsonify({'status': 'ERROR', 'msg': 'ONU não encontrada no sistema FiberHome'})

            # Conectar ao FHManager e obter sinal
            manager = FHManager(olt_ip)
            manager.connect()

            signal_data = manager.get_signal(
                onu_info['placa'],
                onu_info['porta'],
                onu_info['onuid'],
                onu_info['serial'],
                onu_info['nome']
            )

            manager.disconnect()

            # Normalizar dados do sinal
            normalized_signal = normalize_signal_data(signal_data, 'FBT')

            return jsonify({
                'status': 'OK',
                'olt_type': 'FBT',
                'olt_ip': olt_ip,
                'signal': normalized_signal,
                'raw_signal': signal_data  # Manter dados originais para debug
            })

        elif olt_type == 'ZTE':
            # Para ZTE, a conexão é feita automaticamente no construtor
            # Instanciar ZTEManager e obter sinal
            manager = ZTEManager(olt_ip)

            signal_data = manager.show_signal(serial)

            manager.disconnect()

            # Normalizar dados do sinal
            normalized_signal = normalize_signal_data(signal_data, 'ZTE')

            return jsonify({
                'status': 'OK',
                'olt_type': 'ZTE',
                'olt_ip': olt_ip,
                'signal': normalized_signal,
                'raw_signal': signal_data  # Manter dados originais para debug
            })

        else:
            return jsonify({'status': 'ERROR', 'msg': f'Tipo de OLT não suportado: {olt_type}'})

    except Exception as e:
        return jsonify({'status': 'ERROR', 'msg': f'Erro ao consultar sinal: {str(e)}'})
