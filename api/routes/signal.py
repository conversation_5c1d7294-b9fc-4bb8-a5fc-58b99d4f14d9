from flask import request, jsonify, render_template, Blueprint
import json
from utils import *
from db import *
from cli import *
import tasks_fiberhome
import tasks_zte
from rq import Queue, Retry
from rq.job import Job
from worker_config_queue import conn
from dotenv import load_dotenv
import os
from models import token_required
import uuid
from fhmanager import *

signal_route = Blueprint('signal_route', __name__, template_folder='templates')
q = Queue('config_queue', connection=conn)

@signal_route.route('/signal', methods=['GET'])
@token_required
def get_onu_signal():
    return jsonify({'status' : 'OK', 'msg': "Not implemented yet"})
    