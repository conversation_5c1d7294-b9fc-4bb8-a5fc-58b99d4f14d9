from flask import jsonify, Blueprint
from db import *
from models import token_required
from fhmanager import FHManager
from ztemanager import ZTEManager

signal_route = Blueprint('signal_route', __name__, template_folder='templates')

@signal_route.route('/signal/<serial>', methods=['GET'])
@token_required
def get_onu_signal(serial):
    try:
        # Serial vem como parâmetro da URL
        # Verificar tipo de OLT através do serial
        olt_type = get_olt_type_by_serial(serial)

        if not olt_type:
            return jsonify({'status': 'ERROR', 'msg': 'Serial não encontrado ou não vinculado a um OLT'})

        if olt_type == 'FBT':
            # Para FiberHome, precisamos dos parâmetros da ONU autorizada
            onu_info = findauthorizedonu(serial=serial)

            if not onu_info:
                return jsonify({'status': 'ERROR', 'msg': 'ONU não encontrada no sistema FiberHome'})

            # Conectar ao FHManager e obter sinal
            manager = FHManager(onu_info['olt_ip'])
            manager.connect()

            signal_data = manager.get_signal(
                onu_info['placa'],
                onu_info['porta'],
                onu_info['onuid'],
                onu_info['serial'],
                onu_info['nome']
            )

            manager.disconnect()

            return jsonify({
                'status': 'OK',
                'olt_type': 'FBT',
                'signal': signal_data
            })

        elif olt_type == 'ZTE':
            # Para ZTE, podemos chamar diretamente show_signal
            # Primeiro precisamos encontrar o IP da OLT
            onu_info = findauthorizedonu(serial=serial)

            if not onu_info:
                return jsonify({'status': 'ERROR', 'msg': 'ONU não encontrada no sistema ZTE'})

            # Conectar ao ZTEManager e obter sinal
            manager = ZTEManager(onu_info['olt_ip'])
            manager.connect()

            signal_data = manager.show_signal(serial)

            manager.disconnect()

            return jsonify({
                'status': 'OK',
                'olt_type': 'ZTE',
                'signal': signal_data
            })

        else:
            return jsonify({'status': 'ERROR', 'msg': f'Tipo de OLT não suportado: {olt_type}'})

    except Exception as e:
        return jsonify({'status': 'ERROR', 'msg': f'Erro ao consultar sinal: {str(e)}'})
