from flask import jsonify, Blueprint
from db import *
from models import token_required
from fhmanager import FHManager
from ztemanager import ZTEManager

signal_route = Blueprint('signal_route', __name__, template_folder='templates')

@signal_route.route('/signal/<serial>', methods=['GET'])
@token_required
def get_onu_signal(serial):
    try:
        # Serial vem como parâmetro da URL
        # Verificar tipo e IP da OLT através do serial
        olt_info = get_olt_info_by_serial(serial)

        if not olt_info:
            return jsonify({'status': 'ERROR', 'msg': 'Serial não encontrado ou não vinculado a um OLT'})

        olt_type = olt_info['fabricante_modelo']
        olt_ip = olt_info['ip']

        if olt_type == 'FBT':
            # Para FiberHome, precisamos dos parâmetros da ONU autorizada
            onu_info = findauthorizedonu(serial=serial)

            if not onu_info:
                return jsonify({'status': 'ERROR', 'msg': 'ONU não encontrada no sistema FiberHome'})

            # Conectar ao FHManager e obter sinal
            manager = FHManager(olt_ip)
            manager.connect()

            signal_data = manager.get_signal(
                onu_info['placa'],
                onu_info['porta'],
                onu_info['onuid'],
                onu_info['serial'],
                onu_info['nome']
            )

            manager.disconnect()

            return jsonify({
                'status': 'OK',
                'olt_type': 'FBT',
                'olt_ip': olt_ip,
                'signal': signal_data
            })

        elif olt_type == 'ZTE':
            # Para ZTE, podemos chamar diretamente show_signal com o IP da OLT
            # Conectar ao ZTEManager e obter sinal
            manager = ZTEManager(olt_ip)
            manager.connect()

            signal_data = manager.show_signal(serial)

            manager.disconnect()

            return jsonify({
                'status': 'OK',
                'olt_type': 'ZTE',
                'olt_ip': olt_ip,
                'signal': signal_data
            })

        else:
            return jsonify({'status': 'ERROR', 'msg': f'Tipo de OLT não suportado: {olt_type}'})

    except Exception as e:
        return jsonify({'status': 'ERROR', 'msg': f'Erro ao consultar sinal: {str(e)}'})
