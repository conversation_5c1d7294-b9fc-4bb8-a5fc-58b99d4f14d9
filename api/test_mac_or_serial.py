#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de teste para a funcionalidade de MAC ou Serial
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import re

def is_mac_address(input_str):
    """
    Verifica se o input é um endereço MAC
    
    Parameters
    ----------
    input_str : str
        String de entrada
        
    Returns
    -------
    bool
        True se for MAC, False se for serial
    """
    # Remove todos os caracteres não hexadecimais
    clean_input = re.sub(r'[^A-Fa-f0-9]', '', input_str)
    
    # MAC tem exatamente 12 caracteres hexadecimais
    # Serial geralmente tem mais caracteres e pode conter outros caracteres
    if len(clean_input) == 12 and re.match(r'^[A-Fa-f0-9]{12}$', clean_input):
        return True
    return False

def test_mac_detection():
    """Testa a detecção de MAC vs Serial"""
    print("=== Testando detecção de MAC vs Serial ===")
    
    test_cases = [
        # MACs válidos
        ("AA:BB:CC:DD:EE:FF", True, "MAC com dois pontos"),
        ("AA-BB-CC-DD-EE-FF", True, "MAC com hífens"),
        ("AABBCCDDEEFF", True, "MAC sem separadores"),
        ("aa:bb:cc:dd:ee:ff", True, "MAC minúsculo"),
        ("12:34:56:78:9A:BC", True, "MAC misto"),
        
        # Seriais válidos
        ("ZTEGD1EB36A4", False, "Serial ZTE"),
        ("FHTT12345678", False, "Serial FiberHome"),
        ("ALCL12345678", False, "Serial Alcatel"),
        ("HWTC12345678ABCD", False, "Serial longo"),
        ("123456789012345", False, "Serial numérico longo"),
        
        # Casos especiais
        ("AABBCCDDEEFFGG", False, "Muito longo para MAC"),
        ("AABBCCDDEE", False, "Muito curto para MAC"),
        ("GGHHIIJJKKLL", False, "Caracteres inválidos para MAC"),
    ]
    
    for test_input, expected, description in test_cases:
        result = is_mac_address(test_input)
        status = "✓" if result == expected else "✗"
        print(f"{status} {test_input:20} -> {'MAC' if result else 'Serial':8} ({description})")

def test_get_serial_from_mac_or_serial():
    """Testa a função que obtém serial a partir de MAC ou serial"""
    print("\n=== Testando get_serial_from_mac_or_serial ===")
    
    # Importar a função do db.py
    try:
        from db import get_serial_by_mac
        
        # Teste com MAC fictício
        test_mac = "AA:BB:CC:DD:EE:FF"
        print(f"Testando MAC: {test_mac}")
        
        try:
            result = get_serial_by_mac(test_mac)
            print(f"Resultado: {result}")
        except Exception as e:
            print(f"Erro ao buscar serial por MAC: {e}")
        
        # Teste com serial
        test_serial = "ZTEGD1EB36A4"
        print(f"\nTestando Serial: {test_serial}")
        print(f"Resultado: {test_serial} (retornado como está)")
        
    except ImportError as e:
        print(f"Erro ao importar função: {e}")

if __name__ == "__main__":
    test_mac_detection()
    test_get_serial_from_mac_or_serial()
