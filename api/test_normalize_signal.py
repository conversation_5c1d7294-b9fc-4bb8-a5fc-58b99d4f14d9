#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de teste para a função de normalização de sinal
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import re

def normalize_signal_data(signal_data, olt_type):
    """
    Padroniza os dados de sinal para um formato comum
    
    Parameters
    ----------
    signal_data : dict
        Dados brutos do sinal da OLT
    olt_type : str
        Tipo da OLT ('FBT' ou 'ZTE')
        
    Returns
    -------
    dict
        Dados padronizados do sinal
    """
    try:
        normalized = {
            'onu_rx_power': None,  # Sinal de recepção na ONU
            'onu_tx_power': None,  # Sinal de transmissão da ONU  
            'olt_rx_power': None   # Sinal de recepção do OLT
        }
        
        if olt_type == 'FBT':
            # Para FiberHome
            normalized['onu_rx_power'] = signal_data.get('recvpower')
            normalized['onu_tx_power'] = signal_data.get('sendpower') 
            normalized['olt_rx_power'] = signal_data.get('oltrecvpower')
            
        elif olt_type == 'ZTE':
            # Para ZTE, extrair valores numéricos removendo "(dbm)"
            if signal_data.get('down') and signal_data['down'].get('onu'):
                onu_rx = signal_data['down']['onu']
                # Remove "(dbm)" e extrai o número
                normalized['onu_rx_power'] = re.sub(r'\(.*?\)', '', onu_rx).strip()
                
            if signal_data.get('up') and signal_data['up'].get('onu'):
                onu_tx = signal_data['up']['onu']
                normalized['onu_tx_power'] = re.sub(r'\(.*?\)', '', onu_tx).strip()
                
            if signal_data.get('up') and signal_data['up'].get('olt'):
                olt_rx = signal_data['up']['olt']
                normalized['olt_rx_power'] = re.sub(r'\(.*?\)', '', olt_rx).strip()
        
        return normalized
        
    except Exception as e:
        print(f"Erro ao normalizar dados de sinal: {e}")
        return {
            'onu_rx_power': None,
            'onu_tx_power': None,
            'olt_rx_power': None
        }

def test_fbt_normalization():
    """Testa normalização de dados FBT"""
    print("=== Testando normalização FBT ===")
    
    fbt_data = {
        "biascurrent": "11.55",
        "name": "thales.casa",
        "olt": "***********",
        "oltrecvpower": "-19.78",
        "onuid": "3",
        "pon": "10",
        "recvpower": "-16.07",
        "rtt": "1233",
        "sendpower": "2.31",
        "serial": "ZTEGD1EB36A4",
        "slot": "12",
        "temperature": "38.90",
        "voltage": "3.24"
    }
    
    result = normalize_signal_data(fbt_data, 'FBT')
    
    print("Dados originais FBT:")
    print(f"  recvpower: {fbt_data.get('recvpower')}")
    print(f"  sendpower: {fbt_data.get('sendpower')}")
    print(f"  oltrecvpower: {fbt_data.get('oltrecvpower')}")
    
    print("\nDados normalizados:")
    print(f"  onu_rx_power: {result['onu_rx_power']}")
    print(f"  onu_tx_power: {result['onu_tx_power']}")
    print(f"  olt_rx_power: {result['olt_rx_power']}")

def test_zte_normalization():
    """Testa normalização de dados ZTE"""
    print("\n=== Testando normalização ZTE ===")
    
    zte_data = {
        "down": {
            "attenuation": "-22.840(dbm)",
            "olt": "5.522(dbm)",
            "onu": "-22.840(dbm)"
        },
        "up": {
            "attenuation": "2.673(dbm)",
            "olt": "-27.803(dbm)",
            "onu": "2.673(dbm)"
        }
    }
    
    result = normalize_signal_data(zte_data, 'ZTE')
    
    print("Dados originais ZTE:")
    print(f"  down.onu: {zte_data['down']['onu']}")
    print(f"  up.onu: {zte_data['up']['onu']}")
    print(f"  up.olt: {zte_data['up']['olt']}")
    
    print("\nDados normalizados:")
    print(f"  onu_rx_power: {result['onu_rx_power']}")
    print(f"  onu_tx_power: {result['onu_tx_power']}")
    print(f"  olt_rx_power: {result['olt_rx_power']}")

if __name__ == "__main__":
    test_fbt_normalization()
    test_zte_normalization()
